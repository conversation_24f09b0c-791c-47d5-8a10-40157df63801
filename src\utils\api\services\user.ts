import api from '../instance'
import { API_ENDPOINTS } from '../config'
import type { ApiResponse } from '@/types/api'

/**
 * 用户信息类型
 */
export interface UserInfo {
  id: string
  username: string
  email: string
  avatar?: string
  role: string
  permissions: string[]
  createTime: string
  updateTime: string
}

/**
 * 登录参数
 */
export interface LoginParams {
  username: string
  password: string
  captcha?: string
}

/**
 * 登录响应
 */
export interface LoginResponse {
  token: string
  refreshToken: string
  userInfo: UserInfo
  expiresIn: number
}

/**
 * 更新用户信息参数
 */
export interface UpdateUserParams {
  username?: string
  email?: string
  avatar?: string
}

/**
 * 用户相关API
 */
export class UserService {
  /**
   * 用户登录
   */
  static async login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
    return api.post<LoginResponse>(API_ENDPOINTS.USER.LOGIN, params)
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<ApiResponse<null>> {
    return api.post<null>(API_ENDPOINTS.USER.LOGOUT)
  }

  /**
   * 获取用户信息
   */
  static async getUserInfo(): Promise<ApiResponse<UserInfo>> {
    return api.get<UserInfo>(API_ENDPOINTS.USER.INFO)
  }

  /**
   * 更新用户信息
   */
  static async updateUserInfo(params: UpdateUserParams): Promise<ApiResponse<UserInfo>> {
    return api.put<UserInfo>(API_ENDPOINTS.USER.UPDATE, params)
  }
}

export default UserService
