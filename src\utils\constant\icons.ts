// 动态导入所有 SVG 图标
const iconModules = import.meta.glob('@/assets/images/icons/*.svg', { eager: true })

// 创建图标对象
const icons: Record<string, any> = {}

Object.entries(iconModules).forEach(([path, module]) => {
  // 从路径中提取文件名（不包含扩展名）
  const fileName = path.split('/').pop()?.replace('.svg', '') || ''
  // 将文件名转换为组件名格式（首字母大写 + Icon后缀）
  const componentName = fileName + 'Icon'
  icons[componentName] = (module as any).default
})

// 导出所有图标
export const SearchIcon = icons.SearchIcon
export const UserIcon = icons.UserIcon
export const ExportIcon = icons.ExportIcon
export const ChevronLeft20FilledIcon = icons.ChevronLeft20FilledIcon

// 导出整个图标对象：
// 1. 使用具名导入：import { SearchIcon } from './icons'
// 2. 使用默认导入：import icons from './icons'，然后 icons.SearchIcon
export default icons

// 导出所有图标的类型
export type IconName = keyof typeof icons
