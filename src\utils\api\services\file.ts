import api from '../instance'
import { API_ENDPOINTS } from '../config'
import type { ApiResponse } from '@/types/api'

/**
 * 文件信息类型
 */
export interface FileInfo {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  url: string
  uploadTime: string
  uploader: string
}

/**
 * 上传响应
 */
export interface UploadResponse {
  file: FileInfo
  url: string
}

/**
 * 批量上传响应
 */
export interface BatchUploadResponse {
  files: FileInfo[]
  successCount: number
  failCount: number
  errors: string[]
}

/**
 * 文件相关API
 */
export class FileService {
  /**
   * 单文件上传
   */
  static async uploadFile(file: File): Promise<ApiResponse<UploadResponse>> {
    return api.upload<UploadResponse>(API_ENDPOINTS.FILE.UPLOAD, file)
  }

  /**
   * 多文件上传
   */
  static async uploadFiles(files: File[]): Promise<ApiResponse<BatchUploadResponse>> {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    
    return api.upload<BatchUploadResponse>(
      `${API_ENDPOINTS.FILE.UPLOAD}/batch`,
      formData
    )
  }

  /**
   * 文件下载
   */
  static async downloadFile(
    fileId: string,
    filename?: string
  ): Promise<void> {
    return api.download(
      `${API_ENDPOINTS.FILE.DOWNLOAD}/${fileId}`,
      undefined,
      filename
    )
  }

  /**
   * 通过URL下载文件
   */
  static async downloadFileByUrl(
    url: string,
    filename?: string
  ): Promise<void> {
    return api.download(url, undefined, filename)
  }

  /**
   * 获取文件信息
   */
  static async getFileInfo(fileId: string): Promise<ApiResponse<FileInfo>> {
    return api.get<FileInfo>(`${API_ENDPOINTS.FILE.DOWNLOAD}/${fileId}/info`)
  }

  /**
   * 删除文件
   */
  static async deleteFile(fileId: string): Promise<ApiResponse<null>> {
    return api.delete<null>(`${API_ENDPOINTS.FILE.UPLOAD}/${fileId}`)
  }
}

export default FileService
