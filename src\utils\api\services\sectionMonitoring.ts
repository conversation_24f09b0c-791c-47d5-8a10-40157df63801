import api from '../instance'
import { API_ENDPOINTS } from '../config'
import type { PaginatedResponse } from '@/types/api'

/**
 * 断面统计数据类型
 */
export interface SectionStatisticData {
  id: string
  name: string
  volt: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime: string
}

/**
 * 断面统计查询参数
 */
export interface SectionStatisticQueryParams {
  startTime?: string
  endTime?: string
  name?: string
  volt?: string
  status?: string
}

/**
 * 断面监测数据类型
 */
export interface SectionMonitoringData {
  id: string
  sectionName: string
  monitorTime: string
  currentLoad: number
  maxLoad: number
  loadRate: number
  status: 'normal' | 'warning' | 'alarm'
  voltage: number
  frequency: number
}

/**
 * 查询参数
 */
export interface QueryParams {
  startDate?: string
  endDate?: string
  marketType?: string
  status?: string
  page?: number
  pageSize?: number
}

/**
 * 断面监测服务类
 */
export class SectionMonitoringService {
  /**
   * 获取断面统计列表
   */
  static async getSectionStatisticList(
    params?: SectionStatisticQueryParams,
  ): Promise<SectionStatisticData[]> {
    return api.get<SectionStatisticData[]>(API_ENDPOINTS.SPOT_MARKET.SECTION_STATISTIC_LIST, params)
  }

  /**
   * 获取断面监测数据
   */
  static async getSectionMonitoringList(
    params?: QueryParams,
  ): Promise<PaginatedResponse<SectionMonitoringData>['data']> {
    return api.get<PaginatedResponse<SectionMonitoringData>['data']>(
      API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING,
      params,
    )
  }

  /**
   * 获取断面监测详情
   */
  static async getSectionMonitoringDetail(id: string): Promise<SectionMonitoringData> {
    return api.get<SectionMonitoringData>(`${API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING}/${id}`)
  }

  /**
   * 导出断面监测数据
   */
  static async exportSectionMonitoring(params?: QueryParams, filename?: string): Promise<void> {
    return api.download(
      `${API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING}/export`,
      params,
      filename || `section_monitoring_${Date.now()}.xlsx`,
    )
  }

  /**
   * 上传断面监测数据
   */
  static async uploadSectionMonitoring(file: File): Promise<any> {
    return api.upload(`${API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING}/upload`, file)
  }
}
